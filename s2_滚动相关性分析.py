#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
滚动相关性分析脚本
功能：计算两个资产之间的滚动相关性分析
作者：AI Assistant
日期：2025-07-03
"""

import pandas as pd
import numpy as np
import warnings
warnings.filterwarnings('ignore')

# 导入必要的库
try:
    from openpyxl import load_workbook
    from openpyxl.chart import LineChart, Reference, BarChart
    from openpyxl.chart.axis import DateAxis
    from openpyxl.styles import Font
    print("Excel图表库导入成功")
except ImportError as e:
    print(f"警告：Excel图表库导入失败: {e}")
    print("将使用基础Excel保存功能")

# ==================== 配置参数 ====================
# 可配置的资产名称（请根据实际数据列名修改）
ASSET_1 = "中证800全收益"  # 基准资产名称（与其他资产进行相关性分析）

# 多个目标资产列表 - 支持与基准资产进行多重相关性分析
ASSET_2_LIST = [
    "MSCI全球市场股票全收益",
    "Bitcoin",
    "Ethereum",
    "中证全债",
    "COMEX黄金"
]  # 目标资产名称列表

# 可配置的回看窗口参数（天数）
ROLLING_WINDOW = 365*2  # 滚动窗口大小，可选：30, 60, 90等

# 数据文件路径
DATA_FILE = "正文使用/原始指数数据_2010起.xlsx"

# 输出文件路径（将根据分析的资产数量动态生成）
OUTPUT_FILE_PREFIX = f"多资产滚动相关性_{ASSET_1}_{ROLLING_WINDOW}天"

def load_and_preprocess_data(file_path):
    """
    读取并预处理数据
    
    Parameters:
    file_path (str): 数据文件路径
    
    Returns:
    pd.DataFrame: 预处理后的数据
    """
    print("正在读取数据文件...")
    
    try:
        # 读取Excel文件
        raw_data = pd.read_excel(file_path)
        print(f"成功读取数据，原始数据形状: {raw_data.shape}")
        
        # 显示前几行数据以便检查
        print("\n原始数据前5行:")
        print(raw_data.head())
        print("\n数据列名:")
        print(raw_data.columns.tolist())
        
        # 日期列处理
        date_col = raw_data.columns[0]  # 通常是'Date'或类似名称
        print(f"\n使用日期列: {date_col}")
        
        # 转换日期格式
        raw_data[date_col] = pd.to_datetime(raw_data[date_col])
        raw_data = raw_data.set_index(date_col)
        
        # 重新索引为连续日期并向前填充
        print("正在进行日期补全处理...")
        index_new = pd.date_range(start=raw_data.index[0], end=raw_data.index[-1], freq='D') 
        raw_data = raw_data.reindex(index_new)  
        raw_data = raw_data.fillna(method='ffill')
        
        print(f"日期补全后数据形状: {raw_data.shape}")
        print(f"数据日期范围: {raw_data.index[0]} 到 {raw_data.index[-1]}")
        
        return raw_data
        
    except Exception as e:
        print(f"读取数据时发生错误: {e}")
        return None

def check_asset_columns(data, asset1, asset2_list):
    """
    检查指定的资产列是否存在于数据中

    Parameters:
    data (pd.DataFrame): 数据框
    asset1 (str): 基准资产名称
    asset2_list (list): 目标资产名称列表

    Returns:
    tuple: (有效的资产列表, 可用列名列表)
    """
    available_columns = data.columns.tolist()

    # 检查基准资产
    asset1_exists = asset1 in available_columns
    if not asset1_exists:
        print(f"错误: 未找到基准资产 '{asset1}'")
        print("\n可用的列名:")
        for i, col in enumerate(available_columns):
            print(f"{i+1}. {col}")
        return [], available_columns

    # 检查目标资产列表
    valid_assets = []
    missing_assets = []

    for asset2 in asset2_list:
        if asset2 in available_columns:
            valid_assets.append(asset2)
            print(f"✓ 找到资产: {asset2}")
        else:
            missing_assets.append(asset2)
            print(f"✗ 未找到资产: {asset2}")

    if missing_assets:
        print(f"\n警告: 以下资产不存在于数据中: {missing_assets}")
        print("\n可用的列名:")
        for i, col in enumerate(available_columns):
            print(f"{i+1}. {col}")

    if not valid_assets:
        print("错误: 没有找到任何有效的目标资产")
        return [], available_columns

    print(f"\n将分析基准资产 '{asset1}' 与以下 {len(valid_assets)} 个资产的相关性:")
    for i, asset in enumerate(valid_assets, 1):
        print(f"{i}. {asset}")

    return valid_assets, available_columns

def calculate_returns(data, asset1, asset2_list):
    """
    计算多个资产的收益率

    Parameters:
    data (pd.DataFrame): 价格数据
    asset1 (str): 基准资产名称
    asset2_list (list): 目标资产名称列表

    Returns:
    pd.DataFrame: 包含价格和收益率的数据框
    """
    print("正在计算多个资产的收益率...")

    # 创建结果数据框
    result_df = pd.DataFrame(index=data.index)

    # 添加基准资产的价格和收益率
    result_df[f'{asset1}_价格'] = data[asset1]
    result_df[f'{asset1}_收益率'] = data[asset1].pct_change() * 100

    # 添加所有目标资产的价格和收益率
    for asset2 in asset2_list:
        result_df[f'{asset2}_价格'] = data[asset2]
        result_df[f'{asset2}_收益率'] = data[asset2].pct_change() * 100

    # 删除包含NaN的行
    result_df = result_df.dropna()

    print(f"收益率计算完成，有效数据点: {len(result_df)}")
    print(f"包含资产: {asset1} + {len(asset2_list)}个目标资产")

    return result_df

def calculate_rolling_correlation(data, asset1, asset2_list, window):
    """
    计算多个资产与基准资产的滚动相关系数

    Parameters:
    data (pd.DataFrame): 包含收益率的数据框
    asset1 (str): 基准资产名称
    asset2_list (list): 目标资产名称列表
    window (int): 滚动窗口大小

    Returns:
    dict: 包含每个资产对应滚动相关系数的字典
    """
    print(f"正在计算{window}天滚动相关系数...")

    # 获取基准资产收益率
    returns1 = data[f'{asset1}_收益率']

    # 存储所有相关系数结果
    rolling_corr_dict = {}

    for asset2 in asset2_list:
        print(f"  计算 {asset1} vs {asset2} 的相关性...")

        # 获取目标资产收益率
        returns2 = data[f'{asset2}_收益率']

        # 计算滚动相关系数
        rolling_corr = returns1.rolling(window=window).corr(returns2)

        # 删除前面的NaN值
        rolling_corr = rolling_corr.dropna()

        # 存储结果
        rolling_corr_dict[asset2] = rolling_corr

        print(f"    有效数据点: {len(rolling_corr)}")
        print(f"    相关系数范围: {rolling_corr.min():.4f} 到 {rolling_corr.max():.4f}")
        print(f"    平均相关系数: {rolling_corr.mean():.4f}")

    print(f"所有滚动相关系数计算完成，共分析了{len(asset2_list)}个资产对")

    return rolling_corr_dict

def create_final_results(data, rolling_corr_dict, asset1, asset2_list, window):
    """
    创建多个资产的最终结果数据框

    Parameters:
    data (pd.DataFrame): 原始数据
    rolling_corr_dict (dict): 包含每个资产滚动相关系数的字典
    asset1 (str): 基准资产名称
    asset2_list (list): 目标资产名称列表
    window (int): 滚动窗口大小

    Returns:
    pd.DataFrame: 最终结果数据框
    """
    print("正在整理多资产最终结果...")

    # 获取所有相关系数序列的共同索引（取交集确保数据对齐）
    common_index = None
    for asset2 in asset2_list:
        if common_index is None:
            common_index = rolling_corr_dict[asset2].index
        else:
            common_index = common_index.intersection(rolling_corr_dict[asset2].index)

    if common_index is None or len(common_index) == 0:
        print("错误：没有找到共同的有效数据期间")
        return None

    print(f"共同有效数据期间: {len(common_index)} 天")

    # 创建结果数据框
    final_results = pd.DataFrame(index=common_index)

    # 添加日期列
    final_results['日期'] = final_results.index

    # 添加基准资产的价格和收益率
    final_results[f'{asset1}_价格'] = data.loc[common_index, f'{asset1}_价格']
    final_results[f'{asset1}_收益率(%)'] = data.loc[common_index, f'{asset1}_收益率']

    # 添加所有目标资产的价格、收益率和相关系数
    for asset2 in asset2_list:
        # 价格和收益率
        final_results[f'{asset2}_价格'] = data.loc[common_index, f'{asset2}_价格']
        final_results[f'{asset2}_收益率(%)'] = data.loc[common_index, f'{asset2}_收益率']

        # 滚动相关系数
        final_results[f'{asset1}_vs_{asset2}_{window}天相关系数'] = rolling_corr_dict[asset2].loc[common_index]

    # 重置索引，使日期成为普通列
    final_results = final_results.reset_index(drop=True)

    print(f"多资产最终结果整理完成，共{len(final_results)}行数据")
    print(f"包含{len(asset2_list)}个相关性分析结果")

    return final_results

def save_multi_asset_results_to_excel(results, output_file, asset1, asset2_list, window):
    """
    将多资产相关性分析结果保存到Excel文件

    Parameters:
    results (pd.DataFrame): 结果数据框
    output_file (str): 输出文件路径
    asset1 (str): 基准资产名称
    asset2_list (list): 目标资产名称列表
    window (int): 滚动窗口大小
    """
    print(f"正在保存多资产相关性分析结果到: {output_file}")

    try:
        # 使用openpyxl引擎创建Excel文件
        import openpyxl
        with pd.ExcelWriter(output_file, engine='openpyxl', mode='w') as writer:
            # 保存主要结果
            results_copy = results.copy()
            results_copy['日期'] = results_copy['日期'].dt.strftime('%Y-%m-%d')
            results_copy.to_excel(writer, sheet_name='多资产滚动相关性', index=False)

            # 为每个资产创建单独的工作表
            for asset2 in asset2_list:
                corr_col = f'{asset1}_vs_{asset2}_{window}天相关系数'

                # 创建单个资产的数据
                single_asset_data = results_copy[['日期', f'{asset1}_价格', f'{asset1}_收益率(%)',
                                                f'{asset2}_价格', f'{asset2}_收益率(%)', corr_col]].copy()

                # 保存到单独的工作表
                sheet_name = f'{asset1}_vs_{asset2}'[:31]  # Excel工作表名称限制
                single_asset_data.to_excel(writer, sheet_name=sheet_name, index=False)

                # 创建统计摘要
                summary_stats = pd.DataFrame({
                    '统计指标': ['数据点数量', '平均相关系数', '最大相关系数', '最小相关系数',
                              '相关系数标准差', '数据开始日期', '数据结束日期',
                              '高相关期间(>0.5)', '低相关期间(<0.1)'],
                    '数值': [
                        len(results),
                        f"{results[corr_col].mean():.4f}",
                        f"{results[corr_col].max():.4f}",
                        f"{results[corr_col].min():.4f}",
                        f"{results[corr_col].std():.4f}",
                        results['日期'].min().strftime('%Y-%m-%d'),
                        results['日期'].max().strftime('%Y-%m-%d'),
                        f"{(results[corr_col] > 0.5).sum()} 天 ({(results[corr_col] > 0.5).mean()*100:.1f}%)",
                        f"{(results[corr_col] < 0.1).sum()} 天 ({(results[corr_col] < 0.1).mean()*100:.1f}%)"
                    ]
                })

                summary_sheet_name = f'{asset2}_统计'[:31]
                summary_stats.to_excel(writer, sheet_name=summary_sheet_name, index=False)

            # 创建总体统计摘要
            overall_summary = []
            for asset2 in asset2_list:
                corr_col = f'{asset1}_vs_{asset2}_{window}天相关系数'
                overall_summary.append({
                    '资产对': f'{asset1} vs {asset2}',
                    '平均相关系数': f"{results[corr_col].mean():.4f}",
                    '最大相关系数': f"{results[corr_col].max():.4f}",
                    '最小相关系数': f"{results[corr_col].min():.4f}",
                    '标准差': f"{results[corr_col].std():.4f}",
                    '高相关期间(>0.5)': f"{(results[corr_col] > 0.5).mean()*100:.1f}%"
                })

            overall_df = pd.DataFrame(overall_summary)
            overall_df.to_excel(writer, sheet_name='总体统计摘要', index=False)

            # 创建参数说明
            params_info = pd.DataFrame({
                '参数': ['基准资产', '目标资产数量', '目标资产列表', '滚动窗口(天)',
                        '分析日期', '数据来源'],
                '值': [
                    asset1,
                    len(asset2_list),
                    ', '.join(asset2_list),
                    window,
                    pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S'),
                    DATA_FILE
                ]
            })
            params_info.to_excel(writer, sheet_name='参数说明', index=False)

        print(f"多资产相关性分析结果已保存到: {output_file}")
        print(f"包含{len(asset2_list)}个资产的相关性分析")

        return True

    except Exception as e:
        print(f"保存Excel文件时发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def print_multi_asset_analysis_summary(results, asset1, asset2_list, window):
    """
    打印多资产分析结果摘要

    Parameters:
    results (pd.DataFrame): 结果数据框
    asset1 (str): 基准资产名称
    asset2_list (list): 目标资产名称列表
    window (int): 滚动窗口大小
    """
    print("\n" + "=" * 80)
    print("多资产相关性分析结果摘要")
    print("=" * 80)
    print(f"基准资产: {asset1}")
    print(f"目标资产数量: {len(asset2_list)}")
    print(f"滚动窗口: {window}天")
    print(f"分析期间: {results['日期'].min().strftime('%Y-%m-%d')} 至 {results['日期'].max().strftime('%Y-%m-%d')}")
    print(f"有效数据点: {len(results)}天")
    print("-" * 80)

    # 为每个资产打印统计信息
    for i, asset2 in enumerate(asset2_list, 1):
        corr_col = f'{asset1}_vs_{asset2}_{window}天相关系数'

        print(f"{i}. {asset1} vs {asset2}:")
        print(f"   平均相关系数: {results[corr_col].mean():.4f}")
        print(f"   最大相关系数: {results[corr_col].max():.4f}")
        print(f"   最小相关系数: {results[corr_col].min():.4f}")
        print(f"   标准差: {results[corr_col].std():.4f}")
        print(f"   强正相关(>0.5): {(results[corr_col] > 0.5).sum()}天 ({(results[corr_col] > 0.5).mean()*100:.1f}%)")
        print(f"   弱相关(-0.1~0.3): {((results[corr_col] >= -0.1) & (results[corr_col] <= 0.3)).sum()}天 ({((results[corr_col] >= -0.1) & (results[corr_col] <= 0.3)).mean()*100:.1f}%)")
        print(f"   负相关(<-0.1): {(results[corr_col] < -0.1).sum()}天 ({(results[corr_col] < -0.1).mean()*100:.1f}%)")

        if i < len(asset2_list):
            print("-" * 40)

    print("=" * 80)

    # 相关性排序
    print("相关性强度排序（按平均相关系数）:")
    correlations = []
    for asset2 in asset2_list:
        corr_col = f'{asset1}_vs_{asset2}_{window}天相关系数'
        avg_corr = results[corr_col].mean()
        correlations.append((asset2, avg_corr))

    # 按绝对值排序（相关性强度）
    correlations.sort(key=lambda x: abs(x[1]), reverse=True)

    for i, (asset2, avg_corr) in enumerate(correlations, 1):
        print(f"{i}. {asset2}: {avg_corr:.4f}")

    print("=" * 80)

def main():
    """
    主函数：执行完整的多资产滚动相关性分析流程
    """
    print("=" * 80)
    print("多资产滚动相关性分析脚本")
    print("=" * 80)
    print(f"基准资产: {ASSET_1}")
    print(f"目标资产: {', '.join(ASSET_2_LIST)}")
    print(f"滚动窗口: {ROLLING_WINDOW}天")
    print("=" * 80)

    # 1. 读取和预处理数据
    data = load_and_preprocess_data(DATA_FILE)
    if data is None:
        print("数据读取失败，程序终止")
        return

    # 2. 检查资产列是否存在
    valid_assets, _ = check_asset_columns(data, ASSET_1, ASSET_2_LIST)
    if not valid_assets:
        print("没有找到有效的目标资产，程序终止")
        return

    # 3. 计算收益率
    returns_data = calculate_returns(data, ASSET_1, valid_assets)

    # 4. 计算滚动相关系数
    rolling_corr_dict = calculate_rolling_correlation(returns_data, ASSET_1, valid_assets, ROLLING_WINDOW)

    # 5. 创建最终结果
    final_results = create_final_results(returns_data, rolling_corr_dict, ASSET_1, valid_assets, ROLLING_WINDOW)

    # 6. 生成输出文件名
    timestamp = pd.Timestamp.now().strftime("%Y%m%d_%H%M%S")
    output_file = f"{OUTPUT_FILE_PREFIX}_{len(valid_assets)}资产_{timestamp}.xlsx"

    # 7. 保存结果到Excel
    success = save_multi_asset_results_to_excel(final_results, output_file, ASSET_1, valid_assets, ROLLING_WINDOW)

    if success:
        # 8. 打印分析摘要
        print_multi_asset_analysis_summary(final_results, ASSET_1, valid_assets, ROLLING_WINDOW)

        print("\n" + "=" * 80)
        print("多资产相关性分析完成！")
        print(f"结果文件: {output_file}")
        print(f"成功分析了{len(valid_assets)}个资产与{ASSET_1}的相关性")
        print("=" * 80)
    else:
        print("分析过程中出现错误，请检查日志信息")

if __name__ == "__main__":
    main()
