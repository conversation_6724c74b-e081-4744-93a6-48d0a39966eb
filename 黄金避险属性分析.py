#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
黄金避险属性分析脚本
功能：对比分析黄金和比特币的避险特性，使用现代金融计量方法进行严格的统计检验
作者：AI Assistant
日期：2025-01-27
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib
import warnings
from datetime import datetime
import os
warnings.filterwarnings('ignore')

# 导入金融计量分析库
try:
    from arch import arch_model
    print("已导入 arch 库用于 GARCH 建模")
except ImportError:
    print("警告：未安装 arch 库，将使用简化模型")
    arch_model = None

# 导入统计检验库
from scipy import stats
import seaborn as sns

# 设置中文字体
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
matplotlib.rcParams['axes.unicode_minus'] = False
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']

# ==================== 配置参数 ====================
# 数据文件路径
DATA_FILE = "正文使用/原始指数数据_2010起.xlsx"

# 资产分组配置
SAFE_ASSETS = ["COMEX黄金", "Bitcoin"]  # 避险资产候选
RISK_ASSETS_STOCK = ["中证800全收益", "MSCI全球市场股票全收益"]  # 风险资产-股票
RISK_ASSETS_BOND = ["中证全债", "巴克莱彭博全球债"]  # 风险资产-债券
ALL_RISK_ASSETS = RISK_ASSETS_STOCK + RISK_ASSETS_BOND

# 分析参数
QUANTILES = [0.01, 0.025, 0.05]  # 极端分位数阈值
EVENT_WINDOW_LENGTH = 16  # 事件窗口长度 [0, +15]
SIGNIFICANCE_LEVEL = 0.05  # 统计显著性水平

# 输出文件配置
TIMESTAMP = datetime.now().strftime("%Y%m%d_%H%M%S")
OUTPUT_EXCEL = f"黄金避险属性分析结果_{TIMESTAMP}.xlsx"
OUTPUT_FOLDER = "避险分析结果"

def load_and_preprocess_data(file_path):
    """读取并预处理数据"""
    print("正在读取数据文件...")
    
    try:
        # 读取Excel文件
        raw_data = pd.read_excel(file_path)
        print(f"成功读取数据，原始数据形状: {raw_data.shape}")
        
        # 日期列处理
        date_col = raw_data.columns[0]
        print(f"使用日期列: {date_col}")
        
        # 转换日期格式
        raw_data[date_col] = pd.to_datetime(raw_data[date_col])
        raw_data = raw_data.set_index(date_col)
        
        # 重新索引为连续日期并向前填充
        print("正在进行日期补全处理...")
        index_new = pd.date_range(start=raw_data.index[0], end=raw_data.index[-1], freq='D') 
        raw_data = raw_data.reindex(index_new)  
        raw_data = raw_data.fillna(method='ffill')
        
        print(f"日期补全后数据形状: {raw_data.shape}")
        print(f"数据日期范围: {raw_data.index[0]} 到 {raw_data.index[-1]}")
        
        return raw_data
        
    except Exception as e:
        print(f"读取数据时发生错误: {e}")
        return None

def check_asset_availability(data, asset_lists):
    """检查资产可用性"""
    available_columns = data.columns.tolist()
    print(f"\n可用的资产列: {available_columns}")
    
    results = {}
    for name, assets in asset_lists.items():
        available = [asset for asset in assets if asset in available_columns]
        missing = [asset for asset in assets if asset not in available_columns]
        
        if missing:
            print(f"警告：{name} 中缺失资产: {missing}")
        
        results[name] = available
    
    return results

def calculate_returns(data, asset_list):
    """计算资产组的收益率"""
    print(f"正在计算收益率，资产: {asset_list}")
    
    # 检查哪些资产在数据中存在
    available_assets = [asset for asset in asset_list if asset in data.columns and asset.strip() != ""]
    missing_assets = [asset for asset in asset_list if asset not in data.columns or asset.strip() == ""]
    
    if missing_assets:
        print(f"警告：以下资产不存在或为空，将被忽略: {missing_assets}")
    
    if not available_assets:
        print("错误：没有可用的资产")
        return None
    
    print(f"使用的资产: {available_assets}")
    
    # 提取相关资产的价格数据
    price_data = data[available_assets].copy()
    
    # 删除任何包含 NaN 的行
    original_length = len(price_data)
    price_data = price_data.dropna()
    dropped_rows = original_length - len(price_data)
    
    if dropped_rows > 0:
        print(f"删除了 {dropped_rows} 行包含 NaN 的数据")
    
    # 计算日收益率
    returns_data = price_data.pct_change().dropna()
    
    # 再次检查并删除任何剩余的 NaN 值
    before_final_clean = len(returns_data)
    returns_data = returns_data.dropna()
    final_dropped = before_final_clean - len(returns_data)
    
    if final_dropped > 0:
        print(f"计算收益率后删除了额外的 {final_dropped} 行 NaN 数据")
    
    print(f"收益率计算完成，有效数据点: {len(returns_data)}")
    if len(returns_data) > 0:
        print(f"数据期间: {returns_data.index[0].strftime('%Y-%m-%d')} 到 {returns_data.index[-1].strftime('%Y-%m-%d')}")
    
    return returns_data

def align_data(returns_dict):
    """对齐所有资产组的数据时间范围"""
    print("\n正在对齐数据的时间范围...")
    
    # 找到所有数据集的共同日期
    common_dates = None
    for name, returns in returns_dict.items():
        if returns is not None:
            if common_dates is None:
                common_dates = returns.index
            else:
                common_dates = common_dates.intersection(returns.index)
    
    if common_dates is None or len(common_dates) == 0:
        print("错误：没有共同的时间期间")
        return None
    
    # 对齐数据
    aligned_returns = {}
    for name, returns in returns_dict.items():
        if returns is not None:
            aligned_returns[name] = returns.loc[common_dates]
    
    # 删除任何剩余的 NaN 行
    print("正在删除对齐后的 NaN 数据...")
    
    # 合并所有数据来检查 NaN
    combined_data = pd.concat(aligned_returns.values(), axis=1)
    clean_dates = combined_data.dropna().index
    
    # 使用清洁的日期重新对齐
    final_returns = {}
    for name, returns in aligned_returns.items():
        final_returns[name] = returns.loc[clean_dates]
    
    dropped_dates = len(common_dates) - len(clean_dates)
    if dropped_dates > 0:
        print(f"对齐后删除了 {dropped_dates} 行包含 NaN 的数据")
    
    print(f"最终共同数据期间: {len(clean_dates)}天")
    if len(clean_dates) > 0:
        print(f"分析期间: {clean_dates.min()} 到 {clean_dates.max()}")
    
    return final_returns

def create_interaction_variables(returns, quantiles=QUANTILES):
    """创建条件交互变量（参考Baur & Lucey 2010方法）"""
    interactions = {}
    
    for q in quantiles:
        threshold = returns.quantile(q)
        interaction_name = f'D_{int(q*100)}pct'
        interactions[interaction_name] = np.where(returns <= threshold, returns, 0)
        
        # 记录极端事件日期
        extreme_dates = returns[returns <= threshold].index
        interactions[f'{interaction_name}_dates'] = extreme_dates
        
        print(f"分位数 {q*100}%: 阈值={threshold:.4f}, 极端事件数量={len(extreme_dates)}")
    
    return interactions

def estimate_garch_model(dependent_var, independent_vars, model_name):
    """估计GARCH回归模型，遇到arch不支持多元回归时自动降级为OLS"""
    try:
        import statsmodels.api as sm
        if arch_model is not None:
            try:
                X = sm.add_constant(independent_vars)
                model = arch_model(dependent_var, X, vol='GARCH', p=1, q=1, dist='normal')
                results = model.fit(disp='off')
                # arch只支持单变量回归，若报错则降级
                params = results.params
                # arch的结果对象没有std_errors等属性，直接降级
                raise AttributeError('arch不支持多元回归，降级为OLS')
            except Exception as e:
                print(f"GARCH多元回归失败({model_name})，降级为OLS: {e}")
        # OLS回归
        X = sm.add_constant(independent_vars)
        model = sm.OLS(dependent_var, X)
        results = model.fit()
        params = results.params
        std_errors = results.bse
        t_stats = results.tvalues
        p_values = results.pvalues
        return {
            'params': params,
            'std_errors': std_errors,
            't_stats': t_stats,
            'p_values': p_values,
            'aic': results.aic,
            'bic': results.bic,
            'r_squared': results.rsquared,
            'model_type': 'OLS'
        }
    except Exception as e:
        print(f"模型估计失败 {model_name}: {e}")
        return None

def determine_hedge_safe_haven_properties(beta1, beta2, p_beta1, p_beta2, p_beta_sum, significance_level=SIGNIFICANCE_LEVEL):
    """判定避险属性"""
    # 对冲属性判定：β1 ≤ 0 且统计上不显著
    is_hedge = (beta1 <= 0) and (p_beta1 > significance_level)
    
    # 安全港属性判定：β1 + β2 ≤ 0 且统计上显著
    is_safe_haven = (beta1 + beta2 <= 0) and (p_beta_sum <= significance_level)
    
    # 分类结果
    if is_hedge and is_safe_haven:
        return "强避险资产"
    elif is_hedge:
        return "弱避险资产"
    else:
        return "非避险资产"

def calculate_car(asset_returns, benchmark_return, event_dates, window_length=EVENT_WINDOW_LENGTH):
    """计算累积异常收益（CAR）"""
    car_results = []
    valid_events = 0
    
    for event_date in event_dates:
        if event_date in asset_returns.index:
            # 获取事件窗口的收益率
            try:
                window_returns = asset_returns.loc[event_date:].iloc[:window_length]
                if len(window_returns) == window_length:
                    # 计算异常收益
                    abnormal_returns = window_returns - benchmark_return
                    # 计算累积异常收益
                    car_series = abnormal_returns.cumsum()
                    car_results.append(car_series)
                    valid_events += 1
            except:
                continue
    
    if car_results:
        car_df = pd.DataFrame(car_results)
        mean_car = car_df.mean()
        std_car = car_df.std()
        t_stats = mean_car / (std_car / np.sqrt(len(car_results)))
        p_values = 2 * (1 - stats.t.cdf(np.abs(t_stats), len(car_results) - 1))
        
        return {
            'mean_car': mean_car,
            'std_car': std_car,
            't_stats': t_stats,
            'p_values': p_values,
            'valid_events': valid_events
        }
    else:
        return None

def create_data_overview_sheet(returns_dict, writer):
    """创建数据概览工作表"""
    print("正在创建数据概览工作表...")
    
    # 数据概览信息
    overview_data = []
    
    for name, returns in returns_dict.items():
        if returns is not None:
            overview_data.append({
                '资产组': name,
                '资产数量': len(returns.columns),
                '数据期间': f"{returns.index[0].strftime('%Y-%m-%d')} 到 {returns.index[-1].strftime('%Y-%m-%d')}",
                '数据点数': len(returns),
                '年化收益率': returns.mean().mean() * 252,
                '年化波动率': returns.std().mean() * np.sqrt(252)
            })
    
    overview_df = pd.DataFrame(overview_data)
    overview_df.to_excel(writer, sheet_name='数据概览', index=False)
    
    # 添加资产列表
    asset_list_data = []
    for name, returns in returns_dict.items():
        if returns is not None:
            for asset in returns.columns:
                asset_list_data.append({
                    '资产组': name,
                    '资产名称': asset,
                    '年化收益率': returns[asset].mean() * 252,
                    '年化波动率': returns[asset].std() * np.sqrt(252),
                    '夏普比率': (returns[asset].mean() * 252) / (returns[asset].std() * np.sqrt(252))
                })
    
    asset_list_df = pd.DataFrame(asset_list_data)
    asset_list_df.to_excel(writer, sheet_name='资产列表', index=False)

def create_garch_results_sheet(garch_results, writer):
    """创建GARCH回归结果工作表"""
    print("正在创建GARCH回归结果工作表...")
    
    # 整理GARCH结果
    results_data = []
    
    for model_name, result in garch_results.items():
        if result is not None:
            for param_name, value in result['params'].items():
                results_data.append({
                    '模型': model_name,
                    '参数': param_name,
                    '系数': value,
                    '标准误': result['std_errors'].get(param_name, np.nan),
                    't统计量': result['t_stats'].get(param_name, np.nan),
                    'p值': result['p_values'].get(param_name, np.nan),
                    '模型类型': result['model_type'],
                    'AIC': result['aic'],
                    'BIC': result['bic'],
                    'R方': result['r_squared']
                })
    
    if results_data:
        results_df = pd.DataFrame(results_data)
        results_df.to_excel(writer, sheet_name='GARCH回归结果', index=False)

def create_hedge_properties_sheet(hedge_results, writer):
    """创建避险属性判定工作表"""
    print("正在创建避险属性判定工作表...")
    
    # 整理避险属性结果
    properties_data = []
    
    for safe_asset, risk_results in hedge_results.items():
        for risk_asset, quantile_results in risk_results.items():
            for quantile, result in quantile_results.items():
                properties_data.append({
                    '避险资产': safe_asset,
                    '风险资产': risk_asset,
                    '分位数': f"{quantile*100}%",
                    'β1系数': result['beta1'],
                    'β2系数': result['beta2'],
                    'β1+β2': result['beta1'] + result['beta2'],
                    'β1 p值': result['p_beta1'],
                    'β2 p值': result['p_beta2'],
                    'β1+β2 p值': result['p_beta_sum'],
                    '避险属性': result['hedge_property'],
                    '对冲属性': result['is_hedge'],
                    '安全港属性': result['is_safe_haven']
                })
    
    if properties_data:
        properties_df = pd.DataFrame(properties_data)
        properties_df.to_excel(writer, sheet_name='避险属性判定', index=False)

def create_event_study_sheet(event_results, writer):
    """创建事件研究结果工作表"""
    print("正在创建事件研究结果工作表...")
    
    # 整理事件研究结果
    event_data = []
    
    for safe_asset, results in event_results.items():
        if results is not None:
            for day in range(len(results['mean_car'])):
                event_data.append({
                    '避险资产': safe_asset,
                    '事件后天数': day,
                    '累积异常收益': results['mean_car'][day] if hasattr(results['mean_car'], '__getitem__') else results['mean_car'],
                    '标准差': results['std_car'][day] if hasattr(results['std_car'], '__getitem__') else results['std_car'],
                    't统计量': results['t_stats'][day] if hasattr(results['t_stats'], '__getitem__') else results['t_stats'],
                    'p值': results['p_values'][day] if hasattr(results['p_values'], '__getitem__') else results['p_values'],
                    '有效事件数': results['valid_events']
                })
    
    if event_data:
        event_df = pd.DataFrame(event_data)
        event_df.to_excel(writer, sheet_name='事件研究结果', index=False)

def create_summary_sheet(hedge_results, event_results, writer):
    """创建结论汇总工作表"""
    print("正在创建结论汇总工作表...")
    
    # 汇总避险属性
    summary_data = []
    
    for safe_asset, risk_results in hedge_results.items():
        hedge_count = 0
        safe_haven_count = 0
        strong_count = 0
        
        for risk_asset, quantile_results in risk_results.items():
            for quantile, result in quantile_results.items():
                if result['is_hedge']:
                    hedge_count += 1
                if result['is_safe_haven']:
                    safe_haven_count += 1
                if result['hedge_property'] == "强避险资产":
                    strong_count += 1
        
        summary_data.append({
            '避险资产': safe_asset,
            '总测试次数': len(risk_results) * len(QUANTILES),
            '对冲属性次数': hedge_count,
            '安全港属性次数': safe_haven_count,
            '强避险资产次数': strong_count,
            '对冲属性比例': f"{hedge_count/(len(risk_results)*len(QUANTILES))*100:.1f}%",
            '安全港属性比例': f"{safe_haven_count/(len(risk_results)*len(QUANTILES))*100:.1f}%",
            '强避险资产比例': f"{strong_count/(len(risk_results)*len(QUANTILES))*100:.1f}%"
        })
    
    if summary_data:
        summary_df = pd.DataFrame(summary_data)
        summary_df.to_excel(writer, sheet_name='结论汇总', index=False)

def plot_extreme_events(interactions_dict, returns_dict):
    """绘制极端事件时间序列图"""
    print("正在绘制极端事件时间序列图...")
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    fig.suptitle('极端市场事件时间序列分析', fontsize=16, fontweight='bold')
    
    risk_assets = list(interactions_dict.keys())
    
    for i, risk_asset in enumerate(risk_assets[:4]):  # 最多显示4个风险资产
        row, col = i // 2, i % 2
        ax = axes[row, col]
        returns = None
        # 尝试在所有returns_dict中查找该资产
        for group_returns in returns_dict.values():
            if risk_asset in group_returns.columns:
                returns = group_returns[[risk_asset]]
                break
        if returns is None:
            continue
        # 绘制风险资产收益率
        ax.plot(returns.index, returns.iloc[:, 0], alpha=0.7, label=f'{risk_asset}收益率')
        # 标注极端事件
        for q in QUANTILES:
            interaction_name = f'D_{int(q*100)}pct'
            if interaction_name in interactions_dict[risk_asset]:
                extreme_dates = interactions_dict[risk_asset][f'{interaction_name}_dates']
                if len(extreme_dates) > 0:
                    ax.scatter(extreme_dates, returns.loc[extreme_dates].iloc[:, 0], 
                             alpha=0.8, s=30, label=f'{q*100}%分位数事件')
        ax.set_title(f'{risk_asset}极端事件分析')
        ax.set_xlabel('日期')
        ax.set_ylabel('收益率')
        ax.legend()
        ax.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.savefig(f'极端事件时间序列_{TIMESTAMP}.png', dpi=300, bbox_inches='tight')
    plt.show()

def plot_car_comparison(event_results):
    """绘制CAR趋势对比图"""
    print("正在绘制CAR趋势对比图...")
    
    fig, ax = plt.subplots(figsize=(12, 8))
    
    colors = ['gold', 'orange']
    markers = ['o', 's']
    
    for i, (safe_asset, results) in enumerate(event_results.items()):
        if results is not None:
            days = range(len(results['mean_car']))
            mean_car = results['mean_car']
            std_car = results['std_car']
            
            # 绘制CAR曲线
            ax.plot(days, mean_car * 100, color=colors[i], marker=markers[i], 
                   linewidth=2, markersize=6, label=f'{safe_asset} CAR')
            
            # 添加置信区间
            ax.fill_between(days, 
                          (mean_car - 1.96 * std_car / np.sqrt(results['valid_events'])) * 100,
                          (mean_car + 1.96 * std_car / np.sqrt(results['valid_events'])) * 100,
                          alpha=0.2, color=colors[i])
    
    ax.axhline(y=0, color='black', linestyle='--', alpha=0.5)
    ax.set_xlabel('事件后天数')
    ax.set_ylabel('累积异常收益 (%)')
    ax.set_title('黄金 vs 比特币：极端事件后累积异常收益对比')
    ax.legend()
    ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(f'CAR趋势对比_{TIMESTAMP}.png', dpi=300, bbox_inches='tight')
    plt.show()

def plot_hedge_heatmap(hedge_results):
    """绘制避险属性热力图"""
    print("正在绘制避险属性热力图...")
    
    # 准备热力图数据
    safe_assets = list(hedge_results.keys())
    risk_assets = list(hedge_results[safe_assets[0]].keys()) if safe_assets else []
    
    # 创建属性评分矩阵
    hedge_scores = np.zeros((len(safe_assets), len(risk_assets)))
    safe_haven_scores = np.zeros((len(safe_assets), len(risk_assets)))
    
    for i, safe_asset in enumerate(safe_assets):
        for j, risk_asset in enumerate(risk_assets):
            # 计算平均避险属性评分
            hedge_count = 0
            safe_haven_count = 0
            total_tests = 0
            
            for quantile, result in hedge_results[safe_asset][risk_asset].items():
                if result['is_hedge']:
                    hedge_count += 1
                if result['is_safe_haven']:
                    safe_haven_count += 1
                total_tests += 1
            
            if total_tests > 0:
                hedge_scores[i, j] = hedge_count / total_tests
                safe_haven_scores[i, j] = safe_haven_count / total_tests
    
    # 绘制热力图
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    # 对冲属性热力图
    sns.heatmap(hedge_scores, annot=True, fmt='.2f', cmap='RdYlGn', 
                xticklabels=risk_assets, yticklabels=safe_assets,
                ax=ax1, cbar_kws={'label': '对冲属性比例'})
    ax1.set_title('对冲属性热力图')
    ax1.set_xlabel('风险资产')
    ax1.set_ylabel('避险资产')
    
    # 安全港属性热力图
    sns.heatmap(safe_haven_scores, annot=True, fmt='.2f', cmap='RdYlGn',
                xticklabels=risk_assets, yticklabels=safe_assets,
                ax=ax2, cbar_kws={'label': '安全港属性比例'})
    ax2.set_title('安全港属性热力图')
    ax2.set_xlabel('风险资产')
    ax2.set_ylabel('避险资产')
    
    plt.tight_layout()
    plt.savefig(f'避险属性热力图_{TIMESTAMP}.png', dpi=300, bbox_inches='tight')
    plt.show()

def main():
    """主函数：执行完整的黄金避险属性分析流程"""
    print("=" * 80)
    print("黄金避险属性分析脚本")
    print("=" * 80)
    print(f"避险资产候选: {SAFE_ASSETS}")
    print(f"风险资产-股票: {RISK_ASSETS_STOCK}")
    print(f"风险资产-债券: {RISK_ASSETS_BOND}")
    print(f"极端分位数阈值: {[f'{q*100}%' for q in QUANTILES]}")
    print("=" * 80)

    # 1. 读取和预处理数据
    data = load_and_preprocess_data(DATA_FILE)
    if data is None:
        print("数据读取失败，程序终止")
        return

    # 2. 检查资产可用性
    asset_lists = {
        '避险资产': SAFE_ASSETS,
        '风险资产-股票': RISK_ASSETS_STOCK,
        '风险资产-债券': RISK_ASSETS_BOND
    }
    available_assets = check_asset_availability(data, asset_lists)

    # 3. 计算收益率
    returns_dict = {}
    for name, assets in available_assets.items():
        if assets:
            returns = calculate_returns(data, assets)
            if returns is not None:
                returns_dict[name] = returns
    
    # 如果没有风险资产-股票组，单独计算
    if '风险资产-股票' not in returns_dict:
        stock_returns = calculate_returns(data, RISK_ASSETS_STOCK)
        if stock_returns is not None:
            returns_dict['风险资产-股票'] = stock_returns

    # 4. 对齐数据
    aligned_returns = align_data(returns_dict)
    if aligned_returns is None:
        print("数据对齐失败，程序终止")
        return

    # 5. 创建交互变量
    print("\n正在创建极端事件交互变量...")
    interactions_dict = {}
    for risk_asset in ALL_RISK_ASSETS:
        if risk_asset in aligned_returns.get('风险资产-股票', pd.DataFrame()).columns:
            returns = aligned_returns['风险资产-股票'][risk_asset]
        elif risk_asset in aligned_returns.get('风险资产-债券', pd.DataFrame()).columns:
            returns = aligned_returns['风险资产-债券'][risk_asset]
        else:
            continue
        
        interactions_dict[risk_asset] = create_interaction_variables(returns, QUANTILES)

    # 6. GARCH回归分析
    print("\n正在进行GARCH回归分析...")
    garch_results = {}
    hedge_results = {}
    
    for safe_asset in SAFE_ASSETS:
        if safe_asset in aligned_returns.get('避险资产', pd.DataFrame()).columns:
            safe_returns = aligned_returns['避险资产'][safe_asset]
            hedge_results[safe_asset] = {}
            
            for risk_asset in ALL_RISK_ASSETS:
                if risk_asset in interactions_dict:
                    risk_returns = None
                    if risk_asset in aligned_returns.get('风险资产-股票', pd.DataFrame()).columns:
                        risk_returns = aligned_returns['风险资产-股票'][risk_asset]
                    elif risk_asset in aligned_returns.get('风险资产-债券', pd.DataFrame()).columns:
                        risk_returns = aligned_returns['风险资产-债券'][risk_asset]
                    
                    if risk_returns is not None:
                        hedge_results[safe_asset][risk_asset] = {}
                        
                        for quantile in QUANTILES:
                            interaction_name = f'D_{int(quantile*100)}pct'
                            if interaction_name in interactions_dict[risk_asset]:
                                interaction_var = interactions_dict[risk_asset][interaction_name]
                                
                                # 准备回归变量
                                X = pd.DataFrame({
                                    'risk_return': risk_returns,
                                    'interaction': interaction_var
                                })
                                
                                # 估计模型
                                model_name = f"{safe_asset}_vs_{risk_asset}_{int(quantile*100)}pct"
                                result = estimate_garch_model(safe_returns, X, model_name)
                                
                                if result is not None:
                                    garch_results[model_name] = result
                                    
                                    # 提取系数和显著性
                                    beta1 = result['params'].get('risk_return', 0)
                                    beta2 = result['params'].get('interaction', 0)
                                    p_beta1 = result['p_values'].get('risk_return', 1)
                                    p_beta2 = result['p_values'].get('interaction', 1)
                                    
                                    # 计算β1+β2的显著性（简化处理）
                                    p_beta_sum = min(p_beta1, p_beta2)  # 简化处理
                                    
                                    # 判定避险属性
                                    hedge_property = determine_hedge_safe_haven_properties(
                                        beta1, beta2, p_beta1, p_beta2, p_beta_sum)
                                    
                                    hedge_results[safe_asset][risk_asset][quantile] = {
                                        'beta1': beta1,
                                        'beta2': beta2,
                                        'p_beta1': p_beta1,
                                        'p_beta2': p_beta2,
                                        'p_beta_sum': p_beta_sum,
                                        'hedge_property': hedge_property,
                                        'is_hedge': (beta1 <= 0) and (p_beta1 > SIGNIFICANCE_LEVEL),
                                        'is_safe_haven': (beta1 + beta2 <= 0) and (p_beta_sum <= SIGNIFICANCE_LEVEL)
                                    }

    # 7. 事件研究分析
    print("\n正在进行事件研究分析...")
    event_results = {}
    
    # 使用5%分位数作为事件定义
    event_quantile = 0.05
    benchmark_return = 0  # 假设无风险利率为0
    
    for safe_asset in SAFE_ASSETS:
        if safe_asset in aligned_returns.get('避险资产', pd.DataFrame()).columns:
            safe_returns = aligned_returns['避险资产'][safe_asset]
            
            # 找到所有风险资产的极端事件日期
            all_event_dates = set()
            for risk_asset in ALL_RISK_ASSETS:
                if risk_asset in interactions_dict:
                    interaction_name = f'D_{int(event_quantile*100)}pct'
                    if interaction_name in interactions_dict[risk_asset]:
                        extreme_dates = interactions_dict[risk_asset][f'{interaction_name}_dates']
                        all_event_dates.update(extreme_dates)
            
            # 计算CAR
            if all_event_dates:
                event_results[safe_asset] = calculate_car(
                    safe_returns, benchmark_return, list(all_event_dates), EVENT_WINDOW_LENGTH)

    # 8. 创建输出文件夹
    if not os.path.exists(OUTPUT_FOLDER):
        os.makedirs(OUTPUT_FOLDER)

    # 9. 保存Excel结果
    print(f"\n正在保存结果到Excel文件: {OUTPUT_EXCEL}")
    with pd.ExcelWriter(OUTPUT_EXCEL, engine='openpyxl') as writer:
        create_data_overview_sheet(aligned_returns, writer)
        create_garch_results_sheet(garch_results, writer)
        create_hedge_properties_sheet(hedge_results, writer)
        create_event_study_sheet(event_results, writer)
        create_summary_sheet(hedge_results, event_results, writer)

    # 10. 绘制可视化图表
    print("\n正在生成可视化图表...")
    plot_extreme_events(interactions_dict, aligned_returns)
    plot_car_comparison(event_results)
    plot_hedge_heatmap(hedge_results)

    # 11. 打印分析摘要
    print("\n" + "=" * 80)
    print("黄金避险属性分析结果摘要")
    print("=" * 80)
    
    for safe_asset, risk_results in hedge_results.items():
        print(f"\n{safe_asset} 避险属性分析:")
        hedge_count = 0
        safe_haven_count = 0
        strong_count = 0
        total_tests = 0
        
        for risk_asset, quantile_results in risk_results.items():
            for quantile, result in quantile_results.items():
                total_tests += 1
                if result['is_hedge']:
                    hedge_count += 1
                if result['is_safe_haven']:
                    safe_haven_count += 1
                if result['hedge_property'] == "强避险资产":
                    strong_count += 1
        
        print(f"  总测试次数: {total_tests}")
        print(f"  对冲属性次数: {hedge_count} ({hedge_count/total_tests*100:.1f}%)")
        print(f"  安全港属性次数: {safe_haven_count} ({safe_haven_count/total_tests*100:.1f}%)")
        print(f"  强避险资产次数: {strong_count} ({strong_count/total_tests*100:.1f}%)")
    
    print("\n" + "=" * 80)
    print("分析完成！")
    print(f"结果文件: {OUTPUT_EXCEL}")
    print(f"图表文件: 极端事件时间序列_{TIMESTAMP}.png")
    print(f"图表文件: CAR趋势对比_{TIMESTAMP}.png")
    print(f"图表文件: 避险属性热力图_{TIMESTAMP}.png")

if __name__ == "__main__":
    main()